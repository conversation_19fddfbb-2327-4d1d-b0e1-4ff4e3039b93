.fetch-users {
  max-width: 800px;
  margin: 2rem auto;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.fetch-users h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.5rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.fetch-users .loading {
  text-align: center;
  font-size: 1.1rem;
  color: #7f8c8d;
  padding: 2rem;
}

.fetch-users .error {
  background-color: #fee;
  color: #c0392b;
  padding: 1rem;
  border-radius: 4px;
  border-left: 4px solid #e74c3c;
  margin-bottom: 1rem;
}

.fetch-users .users-list {
  list-style: none;
  padding: 0;
}

.fetch-users .user-item {
  background-color: white;
  margin-bottom: 0.75rem;
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid #3498db;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.fetch-users .user-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.fetch-users .user-name {
  color: #2c3e50;
  font-weight: 600;
  margin-right: 0.5rem;
}

.fetch-users .user-email {
  color: #7f8c8d;
  font-style: italic;
}

.fetch-users .user-item::before {
  content: "👤";
  margin-right: 0.5rem;
}
