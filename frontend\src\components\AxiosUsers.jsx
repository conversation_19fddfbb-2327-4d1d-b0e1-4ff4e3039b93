import { useState, useEffect } from 'react'
import axios from 'axios'
import './AxiosUsers.css'

const AxiosUsers = () => {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true)
        const response = await axios.get('https://jsonplaceholder.typicode.com/users')
        setUsers(response.data)
      } catch (err) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [])

  if (loading) return <div className="axios-users"><div className="loading">Loading users...</div></div>
  if (error) return <div className="axios-users"><div className="error">Error: {error}</div></div>

  return (
    <div className="axios-users">
      <h2>Users (Axios)</h2>
      <ul className="users-list">
        {users.map(user => (
          <li key={user.id} className="user-item">
            <span className="user-name">{user.name}</span> - <span className="user-email">{user.email}</span>
          </li>
        ))}
      </ul>
    </div>
  )
}

export default AxiosUsers
