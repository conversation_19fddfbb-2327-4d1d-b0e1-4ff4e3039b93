import { useState, useEffect } from 'react'
import './FetchUsers.css'

const FetchUsers = () => {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true)
        const response = await fetch('https://jsonplaceholder.typicode.com/users')

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        setUsers(data)
      } catch (err) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [])

  if (loading) return <div className="fetch-users"><div className="loading">Loading users...</div></div>
  if (error) return <div className="fetch-users"><div className="error">Error: {error}</div></div>

  return (
    <div className="fetch-users">
      <h2>Users (Fetch API)</h2>
      <ul className="users-list">
        {users.map(user => (
          <li key={user.id} className="user-item">
            <span className="user-name">{user.name}</span> - <span className="user-email">{user.email}</span>
          </li>
        ))}
      </ul>
    </div>
  )
}

export default FetchUsers
