import { useState } from 'react'

const PageScroll = () => {
  const [currentIndex, setCurrentIndex] = useState(0)

  // Sample data for cards
  const cards = [
    { id: 1, title: 'Card 1', content: 'This is the content of card 1' },
    { id: 2, title: 'Card 2', content: 'This is the content of card 2' },
    { id: 3, title: 'Card 3', content: 'This is the content of card 3' },
    { id: 4, title: 'Card 4', content: 'This is the content of card 4' },
    { id: 5, title: 'Card 5', content: 'This is the content of card 5' },
    { id: 6, title: 'Card 6', content: 'This is the content of card 6' },
    { id: 7, title: 'Card 7', content: 'This is the content of card 7' },
    { id: 8, title: 'Card 8', content: 'This is the content of card 8' },
    { id: 9, title: 'Card 9', content: 'This is the content of card 9' },
    { id: 10, title: 'Card 10', content: 'This is the content of card 10' }
  ]

  const goToNext = () => {
    if (currentIndex < cards.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  return (
    <div style={{
      maxWidth: '600px',
      margin: '20px auto',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h2 style={{
        textAlign: 'center',
        color: '#333',
        marginBottom: '30px'
      }}>
        Horizontal Card Scroll
      </h2>

      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <button
          onClick={goToPrevious}
          disabled={currentIndex === 0}
          style={{
            padding: '10px 15px',
            backgroundColor: currentIndex === 0 ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: currentIndex === 0 ? 'not-allowed' : 'pointer',
            marginRight: '20px',
            fontSize: '14px'
          }}
        >
          ← Previous
        </button>

        <div style={{
          display: 'flex',
          overflow: 'hidden',
          width: '350px',
          height: '200px',
          borderRadius: '10px',
          boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
          transform: `translateX(-${currentIndex * 370}px)`,
          transition: 'transform 0.3s ease'
        }}>
          {cards.map(card => (
            <div
              key={card.id}
              style={{
                minWidth: '350px',
                height: '200px',
                padding: '30px',
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
                marginRight: '20px',
                borderRadius: '10px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                textAlign: 'center'
              }}
            >
              <h3 style={{
                color: '#495057',
                marginBottom: '15px',
                fontSize: '24px'
              }}>
                {card.title}
              </h3>
              <p style={{
                color: '#6c757d',
                fontSize: '16px',
                lineHeight: '1.5'
              }}>
                {card.content}
              </p>
            </div>
          ))}
        </div>

        <button
          onClick={goToNext}
          disabled={currentIndex === cards.length - 1}
          style={{
            padding: '10px 15px',
            backgroundColor: currentIndex === cards.length - 1 ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: currentIndex === cards.length - 1 ? 'not-allowed' : 'pointer',
            marginLeft: '20px',
            fontSize: '14px'
          }}
        >
          Next →
        </button>
      </div>

      <div style={{
        textAlign: 'center',
        marginTop: '20px',
        color: '#6c757d',
        fontSize: '14px'
      }}>
        <span>Card {currentIndex + 1} of {cards.length}</span>
      </div>
    </div>
  )
}

export default PageScroll
