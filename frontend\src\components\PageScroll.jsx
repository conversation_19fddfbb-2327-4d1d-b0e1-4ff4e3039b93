import { useState } from 'react'

const PageScroll = () => {
  const [currentIndex, setCurrentIndex] = useState(0)

  // Sample data for cards
  const cards = [
    { id: 1, title: 'Card 1', content: 'This is the content of card 1' },
    { id: 2, title: 'Card 2', content: 'This is the content of card 2' },
    { id: 3, title: 'Card 3', content: 'This is the content of card 3' },
    { id: 4, title: 'Card 4', content: 'This is the content of card 4' },
    { id: 5, title: 'Card 5', content: 'This is the content of card 5' },
    { id: 6, title: 'Card 6', content: 'This is the content of card 6' },
    { id: 7, title: 'Card 7', content: 'This is the content of card 7' },
    { id: 8, title: 'Card 8', content: 'This is the content of card 8' },
    { id: 9, title: 'Card 9', content: 'This is the content of card 9' },
    { id: 10, title: 'Card 10', content: 'This is the content of card 10' }
  ]

  const goToNext = () => {
    if (currentIndex < cards.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  return (
    <div>
      <h2>Horizontal Card Scroll</h2>

      <div style={{ display: 'flex', alignItems: 'center' }}>
        <button
          onClick={goToPrevious}
          disabled={currentIndex === 0}
          style={{ marginRight: '20px' }}
        >
          Previous
        </button>

        <div style={{
          display: 'flex',
          overflow: 'hidden',
          width: '400px',
          transform: `translateX(-${currentIndex * 420}px)`,
          transition: 'transform 0.3s ease'
        }}>
          {cards.map(card => (
            <div
              key={card.id}
              style={{
                minWidth: '400px',
                padding: '20px',
                border: '1px solid #ccc',
                marginRight: '20px'
              }}
            >
              <h3>{card.title}</h3>
              <p>{card.content}</p>
            </div>
          ))}
        </div>

        <button
          onClick={goToNext}
          disabled={currentIndex === cards.length - 1}
          style={{ marginLeft: '20px' }}
        >
          Next
        </button>
      </div>

      <div style={{ textAlign: 'center', marginTop: '20px' }}>
        <span>Card {currentIndex + 1} of {cards.length}</span>
      </div>
    </div>
  )
}

export default PageScroll
